"use client";

import { But<PERSON> } from "@relume_io/relume-ui";
import React, { useEffect, useRef, useState } from "react";

export function Header114() {
  const sectionRef = useRef(null);
  const [isFixed, setIsFixed] = useState(true);

  useEffect(() => {
    const handleScroll = () => {
      if (sectionRef.current) {
        const sectionRect = sectionRef.current.getBoundingClientRect();
        const sectionBottom = sectionRect.bottom;
        const viewportHeight = window.innerHeight;

        // When the section bottom reaches the bottom of the viewport, disable fixed background
        if (sectionBottom <= viewportHeight) {
          setIsFixed(false);
        } else {
          setIsFixed(true);
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <section ref={sectionRef} id="relume" className="relative min-h-[200vh]">
      {/* Sticky background image that stays fixed while content scrolls over it */}
      <div
        className={`${isFixed ? 'fixed' : 'absolute'} top-0 h-screen w-full overflow-hidden -z-50`}
        style={{
          backgroundImage: "url('/images/forestforward/homepage/9.png')",
          backgroundAttachment: isFixed ? "fixed" : "scroll",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat"
        }}
      >
        <div className="absolute inset-0 bg-black/75" />
      </div>

      {/* Content that scrolls over the sticky background */}
      <div className="absolute inset-0 z-50 px-[5%]">
        <div className="container min-h-[200vh] flex flex-col justify-between">
          {/* First content section */}
          <div className="flex flex-col justify-center min-h-screen py-20">
            <div className="w-full max-w-4xl pl-[5%] pr-[20%]">
              <h1 className="text-6xl font-semibold text-text-alternative md:text-9xl lg:text-10xl mb-6 md:mb-8">
                Walk the talk, zeggen ze.<br></br> Wij doen het.
              </h1>
              <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
                <Button title="Button" className="border-none bg-background-primary text-text-primary rounded-lg hover:bg-background-secondary">Ontdek meer</Button>
                <Button
                  title="Button"
                  variant="secondary"
                  className="border border-border/50 bg-transparent text-text-alternative rounded-lg hover:bg-background-primary/50"
                >
                  Contacteer ons
                </Button>
              </div>
            </div>
          </div>

          {/* Second content section */}
          <div className="flex flex-col justify-center min-h-screen py-20">
            <div className="w-full max-w-2xl pl-[40%] pr-[5%]">
              <h1 className="text-4xl font-semibold text-text-alternative md:text-5xl lg:text-6xl mb-6">
                Samen maken we<br></br> duurzaamheid tastbaar.
              </h1>
              <p className="text-text-alternative md:text-md ">
                Klinkt mooi, maar we dóen het ook. Samen met jullie, bedrijven
                groot en klein. En dan hebben we het over duurzaamheid in al z'n
                vormen. Over impact maken. Op alle mogelijke manieren. Dat is de
                meerwaarde van ons ecosysteem. We rollen de mouwen op om samen
                lokaal natuur te creëren en jouw ESG-verhaal tastbaar te maken.
                Om jouw bedrijf een stem te geven, zodat je anderen kan
                inspireren. En om de betrokkenheid van jouw stakeholders te
                vergroten door ze slim én leuk te verbinden. Zorgen voor
                daadkracht. En draagvlak creëren. Inspireren en verbinden. Dat
                is wat we doen!
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}